/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-misused-promises */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToUnicommerce } from '../utils';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';

//import StoreConnectionSteps from './woocommerce-steps';
//import CommerceIntegrationLayout from './commerce-integration-layout';
import image from '../images/integrations/unicommerce.jpg';
//import Input from './Input';
import { ModernIntegrationWrapper } from './integration-components/modern-integration-wrapper';
import { UnicommerceSellerPanel } from './integration-components/unicommerce-component';
import { unicommerceIntegrationSteps } from '../utils/constant';
import { AuthUser } from '../../../types/auth';

interface FormFields {
   channelName: string;
   username: string;
   password: string;
   tenantId: string;
   facility: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

/*const SellerPanel: React.FC<{
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => Promise<void>;
   username: string;
   password: string;
   tenantId: string;
   facility: string;
   apiError: ApiError | null;
}> = ({
   onChange,
   trying,
   onConnect,
   username,
   password,
   tenantId,
   facility,
   apiError,
}) => (
   <div className='seller-panel'>
      <div className='seller-panel__headings'>
         <h5 className='title'>Seller Panel</h5>
         <p className='description'>
            Please provide the following credentials for Unicommerce:
         </p>
      </div>
      <form className='seller-panel__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               type='text'
               id='username'
               label='Username'
               placeholder='Enter your username...'
               name='username'
               value={username}
               onChange={onChange}
            />

            <Input
               type='password'
               id='password'
               label='Password'
               placeholder='Enter your password...'
               name='password'
               value={password}
               onChange={onChange}
            />

            <Input
               type='text'
               id='tenant-id'
               label='Tenant ID'
               placeholder='Enter your tenant ID...'
               name='tenantId'
               value={tenantId}
               onChange={onChange}
            />

            <Input
               type='text'
               id='facility'
               label='Facility'
               placeholder='Enter your facility...'
               name='facility'
               value={facility}
               onChange={onChange}
            />
         </div>
         {apiError && apiError.message && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);*/

const UnicommerceForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: channelNames.UNICOMMERCE,
      username: '',
      password: '',
      tenantId: '',
      facility: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      async (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { username, password, tenantId, facility } = formFields;
         try {
            setTrying(true);
            setApiError({
               success: false,
               message: '',
            });

            await connectDisconnectToUnicommerce({
               channel_name: channelNames.UNICOMMERCE,
               client_id,
               username,
               password,
               tenant_id: tenantId,
               facility,
               isConnect: true,
            });
            setFormFields(defaultState);
            setApiError({
               success: true,
               message: 'Connection Established, Redirecting...',
            });
            setTimeout(() => {
               navigate('/integrations');
            }, 3000);
         } catch (err) {
            const error = err as any;

            const errMessage =
               error.response?.data?.message ||
               'Error connecting to Unicommerce';
            setApiError({
               success: false,
               message: errMessage,
            });
         } finally {
            setTrying(false);
         }
      },
      [formFields, history, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   /*const leftContent = (
      <StoreConnectionSteps
         steps={[
            `Create API User->
Follow the guide to create a user in <User Creation Guide:http://support.unicommerce.com/index.php/knowledge-base/how-to-create-a-user/>.
After creating the user, set a password by referring to this guide:
<Password Reset Guide:http://support.unicommerce.com/index.php/knowledge-base/log-in-to-uniware-with-your-mobile-number-and-reset-password-with-otp/>`,
            `Assigning Facility Access to a User->
Navigate to Settings > Users.
Search or select the user from the list.
Click the Edit button.
Use the dropdown to search and select the required facility.
Click Save.`,
            `Facility Code Reference
Where to Find Facility Code.
Go to Settings > Facilities.
Search or select the relevant facility.
The Facility Code appears in italics before the warehouse display name in the list.`,
            `Ensure Admin Access and Facility Permissions
The API user must have the Admin role.
The user must also have access to the appropriate facility for performing API updates.
Navigate to Settings > Users.
Search or select the desired user.
Under Role, confirm the user is marked as Admin.
Click Edit to view and verify assigned facility access.`,
            `Understanding {tenant}->
The {tenant} is your Uniware account code, visible in the URL after logging in.
Example URL: https://{tenant}.unicommerce.com/`,
         ]}
      />
   );*/

   return (
      <ModernIntegrationWrapper 
      title="Unicommerce Integration "
         description="Connect your  Unicommerce  account to manage your logistics seamlessly"
         logo={image}
         logoAlt="Unicommerce Logo"
         steps={unicommerceIntegrationSteps}
      >
         <UnicommerceSellerPanel
           title="Account Credentials"
            description="Please provide the following credentials for Unicommerce:"
            username={formFields.username}
            password={formFields.password}
            tenantId={formFields.tenantId}
            facility={formFields.facility}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText="Connect to Unicommerce"
            loadingText="Connecting to Unicommerce..."
         />
      </ModernIntegrationWrapper>
   );
};

export default UnicommerceForm;
