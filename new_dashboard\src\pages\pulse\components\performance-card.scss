@use '../../../sass/variable.scss';

.CardWrapper {
   display: flex;
   flex-direction: column;
   align-items: center;
   width: 100%;

   &.light {
      background: #ffffff;
      color: #000000;
   }

   // &.dark {
   //    background: $background;
   //    color: $text_color;
   // }

   .usercard-containers {
      width: 450px;
      min-height: 300px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 13px;
      border-radius: 15px;
      border: 1px solid #ccc;
      background: white;

      .card-top {
         display: flex;
         justify-content: space-between;
         align-items: center;
         gap: 5px;

         .left-section {
            display: flex;
            gap: 5px;
            align-items: center;
         }

         button {
            padding: 6px 8px;
            border: none;
            border-radius: 5px;
            background-color: #e3e2ff;
            color: blue;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;

            .campaign-status-active {
               color: #15994a;
            }

            .campaign-status-pause {
               color: #ffbf00;
            }

            .campaign-status-removed {
               color: #ff0000;
            }
         }

         .optimize-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 6px 12px;
            background: linear-gradient(135deg, #4286f4, #305dd3);
            border: 1px solid #94d0ff;
            border-radius: 999px;
            color: white;
            font-weight: 600;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(50, 90, 200, 0.2);
            min-width: fit-content;

            &:hover {
               transform: translateY(-1px);
               box-shadow: 0 4px 8px rgba(50, 90, 200, 0.3);
            }

            svg {
               width: 12px;
               height: 12px;
               flex-shrink: 0;
            }

            span {
               white-space: nowrap;
               text-align: center;
               line-height: 1;
               display: flex;
               align-items: center;
            }
         }
      }

      .chart-elements {
         display: flex;
         align-items: center;
         justify-content: space-between;
         width: 100%;
         box-sizing: border-box;
         flex-direction: row;

         .elements {
            display: flex;
            flex-direction: column;
            font-weight: bold;
            gap: 10px;

            h4 {
               color: #337cdf;
               font-size: x-large;
               margin: 0;
               font-weight: 700;
            }
         }
      }

      &.light {
         background: #ffffff;
         border: 1px solid #e2e8f0;
      }

      // &.dark {
      //    background: $background_surface;
      //    border: 1px solid #4a5568;
      // }

      .kpi-recommendation {
         display: flex;
         justify-self: flex-start;
         gap: 8px;
         margin-bottom: 12px;

         .campaign-recommendation {
            font-family: 'Poppins';
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            cursor: pointer;

            .campaign-recommendation-good-day {
               padding: 3px 8px;
               border: none;
               border-radius: 4px;
               background-color: #00ff661c;
               color: #15994a;
            }

            .campaign-recommendation-bad-day {
               padding: 3px 8px;
               border: none;
               border-radius: 4px;
               background-color: #f76b6b30;
               color: #f76b6b;
            }
         }
      }

      .divider {
         height: 1px;
         background-color: #ccc;

         &.light {
            border-color: #e2e8f0;
         }

         &.dark {
            border-color: #4a5568;
         }
      }

      .bottom-buttons {
         display: flex;
         justify-content: space-between;
         padding: 10px 0px;
         align-items: center;
         font-size: 15px;

         button.tracking {
            cursor: default;
            color: green;
            font-weight: bold;
         }

         a {
            text-decoration: underline;
            color: #337cdf;
            font-weight: 600;
         }
      }
   }
}
