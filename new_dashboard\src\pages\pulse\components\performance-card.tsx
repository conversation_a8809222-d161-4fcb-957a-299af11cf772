import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../../store/store';
import './performance-card.scss';
import Popup from './pop-up';
import { button } from '../../../utils/strings/pulse-strings';
import { UserDetails } from './interface';
import LineChart from './linechart';
import pulseService from '../../../api/service/pulse';
import { formatValue, toShowCurrency, truncateText } from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   useToast,
   Image,
   Flex,
   Tooltip,
   useColorMode,
   Text,
} from '@chakra-ui/react';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import { pulseMetaKeys } from '../../dashboard/utils/query-keys';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import {
   calculateHelper,
   specialMetrics,
} from '../../utils/kpiCalculaterHelper';
import { DaywiseCampaignKPIsCalculated } from '../../../api/service/pulse/performance-insights/meta-ads';
import { useQueryClient } from '@tanstack/react-query';
import {
   setCurrentSessionID,
   setChunks,
   setCurrentMode,
   setKpiPrompts,
   setKpiPromptsProcessed,
} from '../../../store/reducer/analytics-agent-reducer';

import { getDateRange } from '../utils/helper';
import {
   meta_pulse,
   meta_pulse_scaling,
} from '../../marco/utils/analytics-agent/constants';

interface CardProps {
   campaign: DaywiseCampaignKPIsCalculated;
   tracked: boolean;
   overview?: string;
   performanceTrackBtn?: string;
   performanceTrackBtnId?: string;
}

const PerformanceCard: React.FC<CardProps> = ({
   campaign,
   performanceTrackBtnId,
   overview,
   tracked,
}) => {
   const toast = useToast();
   const queryClient = useQueryClient();
   const colorMode = useColorMode().colorMode;
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { channel, objective, metric } = useAppSelector(
      (state) => state.dropdown,
   );
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const [isPopupOpen, setIsPopupOpen] = useState(false);

   const handlePopupOpen = () => setIsPopupOpen(true);
   const handlePopupClose = () => setIsPopupOpen(false);

   const getKpiDescription = (kpi: string): string => {
      const descriptions: Record<string, string> = {
         roas: 'Return on Ad Spend',
         cpp: 'Cost Per Purchase',
         ctr: 'Click Through Rate',
         cvr: 'Conversion Rate',
         cpl: 'Cost Per Lead',
         cpa: 'Cost Per Acquisition',
         cpc: 'Cost Per Click',
         cpm: 'Cost Per Mille',
         leads: 'Lead Generation',
         purchases: 'Purchase Volume',
         purchase: 'Purchase Volume',
         spend: 'Ad Spend',
         impressions: 'Impression Volume',
         clicks: 'Click Volume',
         reach: 'Reach Volume',
      };
      return descriptions[kpi.toLowerCase()] || kpi.toUpperCase();
   };

   const handleOptimizeClick = () => {
      if (campaign?.campaign_id) {
         const { start_date, end_date, prev_start_date, prev_end_date } =
            getDateRange(dateRange, prevRange);

         const displayPrompt = `Optimization analysis: How can we optimize the ${metric} performance for campaign "${campaign.campaign_name}" in Meta Ads from ${start_date} to ${end_date} compared to ${prev_start_date} to ${prev_end_date}?`;

         // Select prompt set based on the card's color (already calculated above)
         const isGoodPerformance = color === 'green' || color === '#4ADE80';
         const promptSet = isGoodPerformance ? meta_pulse_scaling : meta_pulse;

         // Get the appropriate prompt from selected prompt set
         let kpiPrompt =
            promptSet[metric.toUpperCase() as keyof typeof promptSet];

         if (!kpiPrompt) {
            kpiPrompt = promptSet.ROAS;
         }

         const kpiForTemplate = metric.toUpperCase();

         // Replace template placeholders with actual campaign data
         const aiPrompt = kpiPrompt
            .replace(/{{campaign_id}}/g, campaign.campaign_id)
            .replace(/{{campaign_name}}/g, campaign.campaign_name)
            .replace(/{{objective}}/g, objective)
            .replace(/{{kpi}}/g, kpiForTemplate)
            .replace(/{{kpi_description}}/g, getKpiDescription(metric))
            .replace(/{{start_date}}/g, start_date)
            .replace(/{{end_date}}/g, end_date)
            .replace(/{{prev_start_date}}/g, prev_start_date)
            .replace(/{{prev_end_date}}/g, prev_end_date)
            .replace(/{{currency}}/g, campaign.recent_currency)
            .replace(/\bROAS\b/g, kpiForTemplate)
            .replace(/\bCPP\b/g, kpiForTemplate)
            .replace(/\bPURCHASES\b/g, kpiForTemplate);

         dispatch(setCurrentSessionID(''));
         dispatch(setChunks([]));
         dispatch(setCurrentMode('cmo'));

         // Calculate percentage change for context
         const currentValue = Number(campaign?.current_kpi_val?.[metric]) || 0;
         const prevValue = Number(campaign?.prev_kpi_val?.[metric]) || 0;
         let percentageChange = '';

         if (prevValue !== 0) {
            const change = ((currentValue - prevValue) / prevValue) * 100;
            percentageChange = change.toFixed(1);
         }

         const kpiMetadata = {
            campaign_id: String(campaign.campaign_id),
            campaign_name: String(campaign.campaign_name),
            kpi: String(metric.toUpperCase()),
            currency: String(campaign.recent_currency),
            start_date: String(start_date),
            end_date: String(end_date),
            prev_start_date: String(prev_start_date),
            prev_end_date: String(prev_end_date),
            platform: 'meta_ads',
            campaign_status: String(campaign.recent_campaign_status),
            analysis_type: 'optimization',
            percentage_change: percentageChange,
         };

         dispatch(
            setKpiPrompts({
               displayPrompt,
               aiPrompt,
            }),
         );
         dispatch(setKpiPromptsProcessed(false));

         sessionStorage.setItem(
            'kpi_diagnostics_metadata',
            JSON.stringify(kpiMetadata),
         );

         navigate('/marco/analytics-agent?routedFrom=diagnostic-agent');
      }
   };

   const handleTrackWrapper = () => {
      void trackedKpiService.mutate({
         client_id: userDetails.client_id,
         kpi_name: metric,
         objective: objective,
         campaign_id: campaign.campaign_id,
         tracked: true,
         channel: channel,
      });
   };

   const handleError = (msg: string | null) =>
      toast({
         title: 'Error',
         description: msg,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });

   const trackedKpiService = useApiMutation({
      mutationFn: pulseService.updateTrackedKpis,
      onSuccessHandler: async () => {
         await queryClient.invalidateQueries({
            queryKey: [pulseMetaKeys.trackedCampaigns],
         });
         toast({
            title: 'Tracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
      },
      onError(msg) {
         handleError(msg);
      },
      invalidateCacheQuery: [pulseMetaKeys.metaCampaigns],
   });

   const getSelectedKPI = () => {
      return (
         campaign.kpis.find((kpi) => kpi.kpi_name === metric) ||
         campaign.kpis[0]
      );
   };

   const filteredChartData = campaign?.kpis.filter(
      (kpi) => !isNaN(Number(kpi.kpi_value)) && kpi.kpi_name === metric,
   );

   const kpi_recommendationData = campaign?.daywise_kpis?.[metric]
      ? Object.entries(campaign.daywise_kpis[metric])
           .filter(([, value]) => value !== null)
           .sort(([, valueA], [, valueB]) => valueB - valueA)
      : [];

   const selectedKPI = getSelectedKPI();
   const selectedKPIValue = campaign?.current_kpi_val?.[metric];
   const prevPeriodKPIValue = campaign?.prev_kpi_val?.[metric];
   const { percentage, color, direction, currentValue } = calculateHelper(
      metric,
      selectedKPIValue,
      prevPeriodKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <div className='CardWrapper'>
         <div className={`usercards ${colorMode}`}>
            <div className={`usercard-containers ${colorMode}`}>
               <div className='card-top'>
                  <div className='left-section'>
                     <Tooltip
                        label={
                           campaign.campaign_name.length > 40
                              ? campaign.campaign_name
                              : ''
                        }
                        placement='top'
                        fontSize='small'
                     >
                        <button className='campaign-name'>
                           <span>
                              {truncateText(campaign.campaign_name, false, 40)}
                           </span>
                        </button>
                     </Tooltip>
                     <button className='campaign-status'>
                        <p
                           className={
                              campaign.recent_campaign_status === 'ACTIVE'
                                 ? 'campaign-status-active'
                                 : 'campaign-status-pause'
                           }
                        >
                           {campaign.recent_campaign_status}
                        </p>
                     </button>
                  </div>
                  {campaign.recent_campaign_status === 'ACTIVE' && (
                     <button
                        className='optimize-btn'
                        onClick={handleOptimizeClick}
                        title='Optimize Campaign'
                     >
                        <svg
                           width='16'
                           height='16'
                           viewBox='0 0 24 24'
                           fill='none'
                           xmlns='http://www.w3.org/2000/svg'
                        >
                           <path
                              d='M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z'
                              fill='#4286f4'
                           />
                           <path
                              d='M19 15L19.74 17.74L22.5 18.5L19.74 19.26L19 22L18.26 19.26L15.5 18.5L18.26 17.74L19 15Z'
                              fill='#4286f4'
                           />
                           <path
                              d='M5 6L5.47 7.47L7 8L5.47 8.53L5 10L4.53 8.53L3 8L4.53 7.47L5 6Z'
                              fill='#4286f4'
                           />
                        </svg>
                        <span>Optimize</span>
                     </button>
                  )}
               </div>
               <div className='chart-elements'>
                  <div className='elements'>
                     <h6 style={{ color }}>
                        <span>{percentage && `${percentage}% ${arrow}`}</span>
                     </h6>
                     <p>
                        <span>
                           {selectedKPI.kpi_name.length > 15 ? (
                              <Tooltip
                                 label={selectedKPI.kpi_name.toUpperCase()}
                                 hasArrow
                                 placement='bottom'
                              >{`${selectedKPI.kpi_name.toUpperCase().slice(0, 15)}...`}</Tooltip>
                           ) : (
                              selectedKPI.kpi_name.toUpperCase()
                           )}{' '}
                           {percentage && direction}
                        </span>
                     </p>
                     <h4>
                        <span>
                           {currentValue !== 'N/A' &&
                              toShowCurrency(metric, campaign?.recent_currency)}
                           {currentValue}
                        </span>
                     </h4>
                  </div>
                  <div className='chart' style={{ color: 'black' }}>
                     {campaign &&
                        filteredChartData &&
                        currentValue !== 'N/A' && (
                           <LineChart
                              kpiDetails={{
                                 displayName: campaign.campaign_name,
                                 allData: filteredChartData.map((kpi) => ({
                                    date: kpi.kpi_date,
                                    kpi_value: formatValue(kpi.kpi_value),
                                 })),
                                 stat: campaign.kpis[0].campaign_status,
                              }}
                           />
                        )}
                  </div>
               </div>
               <div className='kpi-recommendation'>
                  {kpi_recommendationData.length > 1 &&
                     kpi_recommendationData?.[0]?.[1] > 0 && (
                        <>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-bad-day' : 'campaign-recommendation-good-day'}`}
                              >
                                 {`Highest on ${
                                    kpi_recommendationData?.[0]?.[1] &&
                                    kpi_recommendationData?.[0]?.[0]
                                 }s : ${Math.floor(
                                    kpi_recommendationData?.[0]?.[1] &&
                                       kpi_recommendationData?.[0]?.[1],
                                 )}`}
                              </p>
                           </button>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-good-day' : 'campaign-recommendation-bad-day'}`}
                              >
                                 {`Lowest on ${
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[0]
                                 }s : ${Math.ceil(
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[1],
                                 )}`}
                              </p>
                           </button>
                        </>
                     )}
               </div>
               <div className='bottom'>
                  <hr className={`divider ${colorMode}`} />
                  <div className='bottom-buttons'>
                     {currentValue === 'N/A' || Number(currentValue) === 0 ? (
                        <Tooltip
                           hasArrow
                           label='No campaign available for this campaign'
                        >
                           <Text cursor='not-allowed'>View Details</Text>
                        </Tooltip>
                     ) : (
                        <Link to='#' onClick={handlePopupOpen} id={overview}>
                           View Details
                        </Link>
                     )}

                     <Flex gap={2}>
                        {!tracked && (
                           <Image
                              src={trackedpin}
                              style={{
                                 filter:
                                    colorMode === 'dark' ? 'invert(1)' : 'none',
                              }}
                           />
                        )}
                        <button
                           id={performanceTrackBtnId}
                           className={`track-button ${tracked ? 'tracking' : ''}`}
                           disabled={tracked}
                           style={{
                              cursor: tracked ? 'not-allowed' : 'pointer',
                           }}
                           onClick={handleTrackWrapper}
                        >
                           {tracked ? button.tracking : button.track}
                        </button>
                     </Flex>
                  </div>
               </div>
            </div>
            {isPopupOpen && (
               <Popup
                  campaign={campaign}
                  isOpen={isPopupOpen}
                  onClose={handlePopupClose}
                  details='Detailed information about'
               />
            )}
         </div>
      </div>
   );
};

export default PerformanceCard;
