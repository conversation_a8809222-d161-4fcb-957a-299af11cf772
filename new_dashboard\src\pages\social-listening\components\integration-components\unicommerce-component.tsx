import React from 'react';
import { <PERSON>, <PERSON><PERSON>ff, <PERSON><PERSON>2, <PERSON>Circle2, AlertCircle, Link } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
//import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/utils';

interface FormFieldProps {
  id: string;
  name: string;
  type: string;
  label: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

interface PasswordFieldProps extends Omit<FormFieldProps, 'type'> {
  showPassword: boolean;
  onTogglePassword: () => void;
}

interface ApiResponseProps {
  success: boolean;
  message: string;
}

interface ModernSellerPanelProps {
  title: string;
  description: string;
  username: string;
  password: string;
  tenantId: string;
  facility: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  apiResponse: ApiResponseProps | null;
  submitButtonText?: string;
  loadingText?: string;
}

export const ModernFormField: React.FC<FormFieldProps> = ({
  id,
  name,
  type,
  label,
  placeholder,
  value,
  onChange,
  required = false,
  disabled = false,
  className
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        id={id}
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
        className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
      />
    </div>
  );
};

export const ModernPasswordField: React.FC<PasswordFieldProps> = ({
  id,
  name,
  label,
  placeholder,
  value,
  onChange,
  showPassword,
  onTogglePassword,
  required = false,
  disabled = false,
  className
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <div className="relative">
        <Input
          id={id}
          name={name}
          type={showPassword ? 'text' : 'password'}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          required={required}
          disabled={disabled}
          className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 pr-10"
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={onTogglePassword}
          disabled={disabled}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-400" />
          ) : (
            <Eye className="h-4 w-4 text-gray-400" />
          )}
        </Button>
      </div>
    </div>
  );
};

export const ModernApiResponse: React.FC<{ response: ApiResponseProps }> = ({ response }) => {
  if (!response.message) return null;

  return (
    <Alert className={cn(
      "border-l-4",
      response.success 
        ? "border-l-green-500 bg-green-50 text-green-800" 
        : "border-l-red-500 bg-red-50 text-red-800"
    )}>
      {response.success ? (
        <CheckCircle2 className="h-4 w-4" />
      ) : (
        <AlertCircle className="h-4 w-4" />
      )}
      <AlertDescription className="font-medium">
        {response.message}
      </AlertDescription>
    </Alert>
  );
};

export const UnicommerceSellerPanel: React.FC<ModernSellerPanelProps> = ({
  title,
  description,
  username,
  password,
  tenantId,
  facility,
  onChange,
  onSubmit,
  isLoading,
  apiResponse,
  submitButtonText = "Connects",
  loadingText = "Connecting..."
}) => {
  const [showPassword, setShowPassword] = React.useState(false);

  const togglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>

      <form onSubmit={onSubmit} className="space-y-6">
        <div className="space-y-4">
          <ModernFormField
            id="username"
            name="username"
            type="text"
            label="Username"
            placeholder="Enter your username..."
            value={username}
            onChange={onChange}
            required
            disabled={isLoading}
          />

          <ModernPasswordField
            id="password"
            name="password"
            label="Password"
            placeholder="Enter your password"
            value={password}
            onChange={onChange}
            showPassword={showPassword}
            onTogglePassword={togglePassword}
            required
            disabled={isLoading}
          />
          <ModernFormField
            id="tenant-id"
            name="tenantId"
            type="text"
            label="Tenant ID"
            placeholder="Enter your tenant ID..."
            value={tenantId}
            onChange={onChange}
            required
            disabled={isLoading}
          />
          <ModernFormField
            id="facility"
            name="facility"
            type="text"
            label="Facility"
            placeholder="Enter your facility..."
            value={facility}
            onChange={onChange}
            required
            disabled={isLoading}
          />
        </div>

        {apiResponse && (
          <ModernApiResponse response={apiResponse} />
        )}

        <Button
          type="submit"
          disabled={isLoading || !username || !password}
          className="w-full h-11 bg-blue-600 hover:bg-blue-700 text-white font-medium"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {loadingText}
            </>
          ) : (
            <>
              <Link className="h-4 w-4 mr-2" />
              {submitButtonText}
            </>
          )}
        </Button>
      </form>
    </div>
  );
};
