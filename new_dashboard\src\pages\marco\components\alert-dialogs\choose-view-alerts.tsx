import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogAction,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';
import { useAppDispatch } from '@/store/store';
import { useNavigate } from 'react-router-dom';

const ChooseViewAlerts = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   return (
      <AlertDialog>
         <AlertDialogTrigger asChild>
            <Button variant='outline' size='lg'>
               View Alerts
            </Button>
         </AlertDialogTrigger>
         <AlertDialogContent className='bg-white dark:bg-gray-800'>
            <AlertDialogHeader>
               <AlertDialogTitle className='font-bold text-xl'>
                  Alert Type Selection
               </AlertDialogTitle>
               <AlertDialogDescription>
                  Choose the type of alerts you want to view. You can either
                  view custom alerts or alerts created by the agent.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel className='font-semibold !text-sm'>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  className='font-normal !text-sm'
                  onClick={() => {
                     dispatch(setCurrentAgent('alerting-agent'));
                     navigate('/marco/custom-alerts/alerts');
                  }}
               >
                  Custom Alerts
               </AlertDialogAction>
               <AlertDialogAction
                  className='font-normal !text-sm'
                  onClick={() => {
                     dispatch(setCurrentAgent('alerting-agent'));
                     navigate('/marco/alerting-agent/alerts');
                  }}
               >
                  Agent Alerts
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
};

export default ChooseViewAlerts;
