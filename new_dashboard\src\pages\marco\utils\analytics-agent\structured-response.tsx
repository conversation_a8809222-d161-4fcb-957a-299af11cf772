import React, { useState } from 'react';
import { Icon, IconButton, Tooltip, useToast } from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { marked } from 'marked';
import { Button } from '@/components/ui/button';
import {
   PiRepeat,
   PiThumbsDown,
   PiThumbsUp,
   PiThumbsDownFill,
   PiThumbsUpFill,
   PiCheckBold,
   PiCopy,
   PiListPlus,
   // PiExportDuotone,
} from 'react-icons/pi';
import {
   AnalyticsAgentChat,
   ChunkData,
} from '@/api/service/agentic-workflow/analytics-agent';
import {
   useLikeDislikeChatMutation,
   useUpdateChatCopiedMutation,
   useUpdateChatFeedbackMutation,
   useUpdateChatRewrittenMutation,
} from '../../apis/analytics-agent-apis';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { useAppSelector } from '@/store/store';
import { useQueryClient } from '@tanstack/react-query';
import { RiCloseLine } from 'react-icons/ri';
import { FEEDBACK_SAMPLES } from './constants';
import ShopifyImage from '../../../../assets/icons/kpi/shopify.png';
import MetaImage from '../../../../assets/icons/kpi/meta.png';
import WebImage from '../../../../assets/icons/kpi/web.png';
import GAdImage from '../../../../assets/icons/kpi/gads.png';
import ASPImage from '../../../../assets/icons/kpi/amazon-seller.png';
import AAdsImage from '../../../../assets/icons/kpi/amazon-ads.png';
import HubspotImage from '../../../../pages/social-listening/images/integrations/hubs.jpeg';

type SourceType = { name: string; image: string }[];

const SOURCES: SourceType = [
   {
      name: 'Shopify Analytics',
      image: ShopifyImage,
   },
   {
      name: 'Google Ads Performance Analytics',
      image: GAdImage,
   },
   {
      name: 'Meta Ads Performance Analytics',
      image: MetaImage,
   },
   {
      name: 'Website Analytics',
      image: WebImage,
   },
   {
      name: 'Amazon Selling Partner Analytics',
      image: ASPImage,
   },
   {
      name: 'Amazon Ads Performance Analytics',
      image: AAdsImage,
   },
   {
      name: 'Hubspot Analytics',
      image: HubspotImage,
   },
];

interface StructuredResponseProps {
   query?: string;
   content: string;
   handleSendPrompt?: (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
      routedFrom?: 'alerting-agent' | 'diagnostic-agent' | null,
   ) => Promise<void>;
   loading?: boolean;
   currentChat?: AnalyticsAgentChat;
   finalInsight?: ChunkData | null;
}

const StructuredResponse: React.FC<StructuredResponseProps> = ({
   query,
   content,
   handleSendPrompt,
   loading = false,
   currentChat,
   finalInsight,
}) => {
   const queryClient = useQueryClient();
   const toast = useToast();

   const [copied, setCopied] = useState(false);
   const [feedbackOpen, setFeedbackOpen] = useState({
      open: false,
      status: '' as 'liked' | 'disliked' | '',
   });
   const [customFeedbackOpen, setCustomFeedbackOpen] = useState(false);
   const [customFeedback, setCustomFeedback] = useState('');

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   // Helper function to get KPI description
   const getKpiDescription = (kpi: string): string => {
      const descriptions: Record<string, string> = {
         roas: 'Return on Ad Spend',
         cpp: 'Cost Per Purchase',
         ctr: 'Click Through Rate',
         cvr: 'Conversion Rate',
         cpl: 'Cost Per Lead',
         cpa: 'Cost Per Acquisition',
         cpc: 'Cost Per Click',
         cpm: 'Cost Per Mille',
         leads: 'Lead Generation',
         purchases: 'Purchase Volume',
         purchase: 'Purchase Volume', // Handle both singular and plural
         spend: 'Ad Spend',
         impressions: 'Impression Volume',
         clicks: 'Click Volume',
         reach: 'Reach Volume',
      };
      return descriptions[kpi.toLowerCase()] || kpi.toUpperCase();
   };
   const { currentSessionID } = useAppSelector((state) => state.analyticsAgent);

   const { mutateAsync: likeDislikeChat } = useLikeDislikeChatMutation();
   const { mutateAsync: updateChatRewritten } =
      useUpdateChatRewrittenMutation();
   const { mutateAsync: updateChatCopied } = useUpdateChatCopiedMutation();
   const { mutateAsync: updateChatFeedback } = useUpdateChatFeedbackMutation();

   const responseMatch = content.match(/^(.*?)<follow_up_questions>/s);
   const responseRaw = responseMatch?.[1]?.trim() || content.trim();

   const followUpMatches = [
      ...content.matchAll(
         /<follow_up_question(?:\d*)?>(.*?)<\/follow_up_question(?:\d*)?>/gs,
      ),
   ];
   const followUpQuestions = followUpMatches.map((match) => match[1].trim());

   const [formattedResponse, setFormattedResponse] = useState<string>('');

   function addTableStylesToHtmlString(htmlString: string) {
      const tableStyle = `
            width:100%;
            border-collapse:collapse;
            margin:24px 0;
            font-family:'Segoe UI', Tahoma, sans-serif;
            font-size:15px;
            border:1px solid #ddd;
            box-shadow:0 2px 6px rgba(0,0,0,0.05);
            background-color:#fff;
         `
         .replace(/\s+/g, ' ')
         .trim();

      const thStyle = `
            background-color:#f9fafb;
            padding:12px;
            text-align:left;
            font-weight:600;
            border:1px solid #ddd;
            color:#333;
         `
         .replace(/\s+/g, ' ')
         .trim();

      const tdStyle = `
            padding:12px;
            border:1px solid #ddd;
            vertical-align:top;
            color:#333;
         `
         .replace(/\s+/g, ' ')
         .trim();

      const trStyle = `
            border-bottom:1px solid #eee;
         `
         .replace(/\s+/g, ' ')
         .trim();

      return (
         htmlString
            // Table
            .replace(/<table\b(?![^>]*style=)/g, `<table style="${tableStyle}"`)
            .replace(
               /<table\b([^>]*?)style="([^"]*?)"/g,
               (_, attrs, existing) => {
                  return `<table${attrs}style="${existing};${tableStyle}"`;
               },
            )

            // TH
            .replace(/<th\b(?![^>]*style=)/g, `<th style="${thStyle}"`)
            .replace(/<th\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
               return `<th${attrs}style="${existing};${thStyle}"`;
            })

            // TD
            .replace(/<td\b(?![^>]*style=)/g, `<td style="${tdStyle}"`)
            .replace(/<td\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
               return `<td${attrs}style="${existing};${tdStyle}"`;
            })

            // TR
            .replace(/<tr\b(?![^>]*style=)/g, `<tr style="${trStyle}"`)
            .replace(/<tr\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
               return `<tr${attrs}style="${existing};${trStyle}"`;
            })
      );
   }

   React.useEffect(() => {
      const getFormattedResponse = async () => {
         const result = await marked.parse(
            addTableStylesToHtmlString(responseRaw),
            { async: true },
         );
         setFormattedResponse(result);
      };

      void getFormattedResponse();
   }, [responseRaw]);

   const stripHtmlTags = (input: string): string => {
      return input
         .replace(/<br\s*\/?>/gi, '\n')
         .replace(/<\/?strong>/gi, '**')
         .replace(/<\/?[^>]+(>|$)/g, '');
   };

   // const handleExport = async () => {
   //    if (handleSendPrompt) {
   //    }
   // };

   const handleRewrite = async () => {
      if (handleSendPrompt) {
         if (
            currentChat?.diagnostics_prompt_meta_data &&
            Object.keys(currentChat.diagnostics_prompt_meta_data).length > 0
         ) {
            const metadata = currentChat.diagnostics_prompt_meta_data;
            const {
               kpi,
               currency,
               start_date,
               end_date,
               prev_start_date,
               prev_end_date,
               platform,
               percentage_change,
               campaign_name,
               analysis_type,
            } = metadata;

            // Import prompt sets
            const { KPI_PROMPTS, GOOGLE_ADS_KPI_PROMPTS, meta_pulse } =
               await import('../../utils/analytics-agent/constants');

            // Determine platform and select correct prompt set
            const isGoogleAds =
               platform === 'googleads' ||
               (currentChat?.channel &&
                  currentChat.channel.toLowerCase().includes('google'));

            let kpiPrompt: string | undefined;

            // Use meta_pulse for optimization requests, otherwise use regular prompts
            if (analysis_type === 'optimization' && !isGoogleAds) {
               kpiPrompt = meta_pulse[kpi as keyof typeof meta_pulse];
            } else if (isGoogleAds) {
               kpiPrompt =
                  GOOGLE_ADS_KPI_PROMPTS[
                     kpi as keyof typeof GOOGLE_ADS_KPI_PROMPTS
                  ];
            } else {
               kpiPrompt = KPI_PROMPTS[kpi as keyof typeof KPI_PROMPTS];
            }

            if (kpiPrompt) {
               // Build AI prompt based on prompt type
               let aiPrompt: string;

               if (analysis_type === 'optimization' && !isGoogleAds) {
                  // Use meta_pulse template format for optimization
                  aiPrompt = kpiPrompt
                     .replace(/{{campaign_id}}/g, metadata.campaign_id || '')
                     .replace(
                        /{{campaign_name}}/g,
                        metadata.campaign_name || '',
                     )
                     .replace(/{{objective}}/g, metadata.objective || '')
                     .replace(/{{kpi}}/g, kpi || '')
                     .replace(/{{kpi_description}}/g, getKpiDescription(kpi))
                     .replace(/{{start_date}}/g, start_date || '')
                     .replace(/{{end_date}}/g, end_date || '')
                     .replace(/{{prev_start_date}}/g, prev_start_date || '')
                     .replace(/{{prev_end_date}}/g, prev_end_date || '')
                     .replace(/{{currency}}/g, currency || '');
               } else {
                  // Use regular KPI_PROMPTS template format
                  aiPrompt = kpiPrompt
                     .replace(/{{client_id}}/g, client_id || '')
                     .replace(/{{KPI}}/g, kpi || '')
                     .replace(/{{start_date}}/g, start_date || '')
                     .replace(/{{end_date}}/g, end_date || '')
                     .replace(/{{prev_start_str}}/g, prev_start_date || '')
                     .replace(/{{prev_end_str}}/g, prev_end_date || '')
                     .replace(/{{currency}}/g, currency || '');
               }

               const platformName = isGoogleAds ? 'Google Ads' : 'Meta Ads';

               let displayPrompt = '';

               if (
                  percentage_change !== undefined &&
                  percentage_change !== null
               ) {
                  const percentageVal = parseFloat(percentage_change);

                  if (!isNaN(percentageVal)) {
                     const changeDirection =
                        percentageVal > 0 ? 'increase' : 'decrease';
                     const absPercentageChange =
                        Math.abs(percentageVal).toFixed(1);

                     // Include campaign name if available and it's an optimization request
                     const campaignContext =
                        campaign_name && analysis_type === 'optimization'
                           ? `campaign '${campaign_name}' in `
                           : '';

                     displayPrompt = `Diagnostic analysis: Root cause of why ${kpi} ${changeDirection} by ${absPercentageChange}% in ${campaignContext}${platformName} from ${start_date} to ${end_date} compared to ${prev_start_date} to ${prev_end_date}?`;
                  } else {
                     // Fallback if percentage change is not a valid number
                     const campaignContext =
                        campaign_name && analysis_type === 'optimization'
                           ? `campaign '${campaign_name}' in `
                           : '';
                     displayPrompt = `Diagnostic analysis: Root cause of change in ${kpi} in ${campaignContext}${platformName} from ${start_date} to ${end_date} compared to ${prev_start_date} to ${prev_end_date}?`;
                  }
               } else {
                  // Fallback if we don't have percentage change data
                  const campaignContext =
                     campaign_name && analysis_type === 'optimization'
                        ? `campaign '${campaign_name}' in `
                        : '';
                  displayPrompt = `Diagnostic analysis: Root cause of change in ${kpi} in ${campaignContext}${platformName} from ${start_date} to ${end_date} compared to ${prev_start_date} to ${prev_end_date}?`;
               }

               void handleSendPrompt(
                  displayPrompt,
                  aiPrompt,
                  'diagnostic-agent',
               );
            } else {
               // Fallback for missing prompt template
               const platformName = isGoogleAds ? 'Google Ads' : 'Meta Ads';
               const campaignContext =
                  campaign_name && analysis_type === 'optimization'
                     ? `campaign '${campaign_name}' in `
                     : '';
               const fallbackPrompt = `Please analyze the ${kpi} performance in ${campaignContext}${platformName} from ${start_date} to ${end_date} compared to ${prev_start_date} to ${prev_end_date}.`;
               void handleSendPrompt(
                  fallbackPrompt,
                  fallbackPrompt,
                  'diagnostic-agent',
               );
            }
         } else {
            // No metadata available, use original query
            void handleSendPrompt(query || '');
         }

         await updateChatRewritten({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
            chat_id: currentChat?.chat_id || '',
         });
      }
   };

   const handleCopyResponse = async () => {
      try {
         await navigator.clipboard.writeText(stripHtmlTags(formattedResponse));
         await updateChatCopied({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
            chat_id: currentChat?.chat_id || '',
         });
         setCopied(true);
         setTimeout(() => setCopied(false), 2000);
      } catch (err) {
         console.error('Failed to copy text: ', err);
      }
   };

   const handleClickLike = async (action: 'liked' | 'disliked') => {
      void (await handleLikeDislikeChat(action));
      setFeedbackOpen({
         open: true,
         status: action,
      });
   };

   const handleLikeDislikeChat = async (action: 'liked' | 'disliked') => {
      await likeDislikeChat({
         client_id: client_id || '',
         user_id: user_id || '',
         session_id: currentSessionID || '',
         chat_id: currentChat?.chat_id || '',
         action,
      });

      await queryClient.invalidateQueries({
         queryKey: ['sessionHistory'],
      });
   };

   const handleUpdateFeedback = async (feedback: string) => {
      setFeedbackOpen({ open: false, status: '' });

      await updateChatFeedback({
         client_id: client_id || '',
         user_id: user_id || '',
         session_id: currentSessionID || '',
         chat_id: currentChat?.chat_id || '',
         type: 'existing',
         feedback,
      });

      toast({
         title: 'Feedback submitted',
         description: `Your feedback has been recorded.`,
         status: 'success',
         duration: 3000,
         isClosable: true,
      });
   };

   const handleClickOther = () => {
      setCustomFeedbackOpen(true);
   };

   const handleKeyDown = async (
      e: React.KeyboardEvent<HTMLTextAreaElement>,
   ) => {
      if (e.key === 'Enter') {
         e.preventDefault();

         if (customFeedback.trim() === '') {
            toast({
               title: 'Feedback required',
               description: `Please provide your feedback before submitting.`,
               status: 'warning',
               duration: 3000,
               isClosable: true,
            });
            return;
         }

         setFeedbackOpen({ open: false, status: '' });
         setCustomFeedback('');
         setCustomFeedbackOpen(false);

         await updateChatFeedback({
            client_id: client_id || '',
            user_id: user_id || '',
            session_id: currentSessionID || '',
            chat_id: currentChat?.chat_id || '',
            type: 'custom',
            feedback: customFeedback.trim(),
         });

         toast({
            title: 'Feedback submitted',
            description: `Your feedback has been recorded.`,
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
      }
   };

   return (
      <div>
         <p
            className='text-[14px] md:text-[16px] leading-[1.75] text-black'
            dangerouslySetInnerHTML={{
               __html:
                  formattedResponse ||
                  'Something went wrong. Please try again or rephrase your question.',
            }}
         />
         {!loading && (
            <div>
               {finalInsight &&
                  Array.isArray(finalInsight.metadata?.sources) &&
                  finalInsight?.metadata?.sources?.length > 0 &&
                  SOURCES.some(
                     (src) =>
                        Array.isArray(finalInsight.metadata.sources) &&
                        finalInsight.metadata.sources.includes(src.name),
                  ) && (
                     <div className='w-max mt-4 flex items-center border-2 gap-1 px-3 py-2 font-semibold rounded-full border-gray-600 text-gray-600'>
                        {finalInsight.metadata.sources.map(
                           (
                              source:
                                 | 'Shopify Analytics'
                                 | 'Google Ads Performance Analytics'
                                 | 'Meta Ads Performance Analytics'
                                 | 'Website Analytics'
                                 | 'Amazon Selling Partner Analytics'
                                 | 'Amazon Ads Performance Analytics'
                                 | 'Hubspot Analytics',
                           ) => (
                              <div key={source} className='flex items-center'>
                                 <img
                                    src={
                                       SOURCES.find((s) => s.name === source)
                                          ?.image || ''
                                    }
                                    alt={source}
                                    className='w-5 h-5 mr-1'
                                 />
                              </div>
                           ),
                        )}
                        <span className='text-[12px]'>
                           {finalInsight.metadata.sources.length > 1
                              ? 'Sources'
                              : 'Source'}
                        </span>
                     </div>
                  )}
               <div className='flex items-center justify-between mt-4'>
                  <div className='flex items-center gap-2'>
                     {/* <Button
                        className='text-sm text-black p-[3px] hover:cursor-pointer'
                        onClick={() => void handleExport()}
                        variant='ghost'
                        disabled={loading}
                     >
                        <PiExportDuotone />
                        Export
                     </Button> */}
                     <Button
                        className='text-sm text-black p-[3px] hover:cursor-pointer'
                        onClick={() => void handleRewrite()}
                        variant='ghost'
                        disabled={loading}
                     >
                        <PiRepeat />
                        Rewrite
                     </Button>
                  </div>
                  <div className='flex'>
                     <IconButton
                        aria-label='like'
                        variant='ghost'
                        cursor='pointer'
                        onClick={() => void handleClickLike('liked')}
                     >
                        <Tooltip label='Helpful' hasArrow placement='top'>
                           <span>
                              {currentChat?.response_like_dislike ===
                              'liked' ? (
                                 <PiThumbsUpFill />
                              ) : (
                                 <PiThumbsUp />
                              )}
                           </span>
                        </Tooltip>
                     </IconButton>
                     <IconButton
                        aria-label='dislike'
                        variant='ghost'
                        cursor='pointer'
                        onClick={() => void handleClickLike('disliked')}
                     >
                        <Tooltip label='Not helpful' hasArrow placement='top'>
                           <span>
                              {currentChat?.response_like_dislike ===
                              'disliked' ? (
                                 <PiThumbsDownFill />
                              ) : (
                                 <PiThumbsDown />
                              )}
                           </span>
                        </Tooltip>
                     </IconButton>
                     <IconButton
                        aria-label='copy'
                        variant='ghost'
                        cursor='pointer'
                        onClick={() => void handleCopyResponse()}
                     >
                        {copied ? <PiCheckBold /> : <PiCopy />}
                     </IconButton>
                  </div>
               </div>
               {feedbackOpen.open && (
                  <div className='flex flex-col w-[100%] border-2 border-gray-200! text-gray-600 rounded-sm items-start justify-between py-2 my-4'>
                     <div className='flex w-[95%] items-center justify-between gap-2 mx-4 hover:cursor-pointer'>
                        <span className='text-sm mt-2'>
                           What did you like about this response? (optional)
                        </span>
                        <RiCloseLine
                           onClick={() => {
                              setFeedbackOpen({ open: false, status: '' });
                              setCustomFeedbackOpen(false);
                              setCustomFeedback('');
                           }}
                        />
                     </div>
                     {customFeedbackOpen ? (
                        <>
                           <div className='flex w-[100%] items-center gap-2 mx-4 my-2 flex-wrap'>
                              <textarea
                                 className='w-[96%] h-[100px] p-2 border border-gray-300 rounded-md text-sm resize-none'
                                 placeholder='Please provide your feedback...'
                                 value={customFeedback}
                                 onChange={(e) =>
                                    setCustomFeedback(e.target.value)
                                 }
                                 onKeyDown={(e) => void handleKeyDown(e)}
                              />
                           </div>
                        </>
                     ) : (
                        <>
                           <div className='flex items-center gap-2 mx-4 my-2 flex-wrap'>
                              {FEEDBACK_SAMPLES?.[
                                 feedbackOpen?.status as keyof typeof FEEDBACK_SAMPLES
                              ]?.map((sample, index) => (
                                 <Button
                                    key={index}
                                    className='text-sm w-[267px] border-gray-200! text-gray-600 p-[3px] hover:cursor-pointer'
                                    variant='outline'
                                    onClick={() =>
                                       void handleUpdateFeedback(sample)
                                    }
                                 >
                                    {sample}
                                 </Button>
                              ))}
                              <Button
                                 className='text-sm w-[267px] border-gray-200! text-gray-600 p-[3px] hover:cursor-pointer'
                                 variant='outline'
                                 onClick={() => void handleClickOther()}
                                 disabled={loading}
                              >
                                 Other...
                              </Button>
                           </div>
                        </>
                     )}
                  </div>
               )}
            </div>
         )}
         {followUpQuestions.length > 0 && (
            <div className='mt-4'>
               <div className='flex items-center gap-2 mb-4 text-black'>
                  <Icon as={PiListPlus} />
                  <p className='font-bold'>More like this</p>
               </div>
               <ul className='list-none'>
                  {followUpQuestions.map((q, idx) => (
                     <li
                        key={idx}
                        onClick={() => {
                           if (handleSendPrompt) void handleSendPrompt(q);
                        }}
                        className='flex items-start justify-between border-t-[1px] border-gray-200 px-[2px] py-[4px] hover:bg-gray-50 hover:cursor-pointer'
                     >
                        <p className='w-[95%] text-sm text-gray-800 leading-[1.75] font-bold hover:color-blue-500'>
                           {q}
                        </p>
                        <AddIcon boxSize={3} color='gray.800' mt={1} />
                     </li>
                  ))}
               </ul>
            </div>
         )}
      </div>
   );
};

export default StructuredResponse;
