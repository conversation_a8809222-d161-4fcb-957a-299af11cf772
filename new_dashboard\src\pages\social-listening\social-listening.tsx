import { Box, Flex, Heading } from '@chakra-ui/react';
import TooltipIcon from '../../components/info-icon-content/tooltip-message';

import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import {
   AmazonAds,
   AmazonSellerPartner,
   FacebookAds,
   GoogleAds,
   GoogleSearchConsole,
   Hubspot,
   Integration,
   IThinkLogistics,
   Shopify,
   ShipRocket,
} from './components';

import './social-listening.scss';
import { useEffect } from 'react';
import { content } from '../../components/info-icon-content/info-content';
import { OptimisationStatusKeys } from '../../layouts/app-layout';
import Unicommerce from './components/Unicommerce';
import WhatsApp from './components/Whatsapp';
import GoogleAnalytics from './components/google-analytics';

const SocialListening: React.FC = () => {
   const intro = introJs();
   const dispatch = useAppDispatch();

   const { integration } = useAppSelector((state) => state.tour);
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   const integrations: {
      title: string;
      componentKey: OptimisationStatusKeys;
      components: React.FC[];
      completed: boolean;
   }[] = [
      {
         title: 'CRM, Channels & Marketplace',
         componentKey: 'channels_marketplace',
         components: [
            Shopify,
            Hubspot,
            AmazonSellerPartner,
            Unicommerce,
            
         ],
         completed: optimisationsStatus.channels_marketplace,
      },
       {
         title: 'Shipping and Logistics',
         componentKey: 'shipping_logistics',
         components: [
           
            IThinkLogistics,
            ShipRocket,
         ],
         completed: optimisationsStatus.channels_marketplace,
      },
      {
         title: 'Ads Account',
         components: [GoogleAds, FacebookAds, AmazonAds],
         componentKey: 'ads_account',
         completed: optimisationsStatus.ads_account,
      },
      {
         title: 'SEO',
         components: [GoogleSearchConsole, GoogleAnalytics],
         componentKey: 'seo',
         completed: optimisationsStatus.seo,
      },
      {
         title: 'Socials',
         components: [WhatsApp],
         componentKey: 'socials',
         completed: optimisationsStatus.complete,
      },
   ];

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
      available?: boolean;
   }[] = [
      {
         element: '#integrationCategory-channels---marketplace',
         intro: 'Easily integrate your ads platforms, social media, marketplace, and website to Flable AI to get a unified access to your data for better decision making.',
         position: 'top',
         available: integrations.some(
            (integration) =>
               integration.title === 'Channels & Marketplace' &&
               integration.completed,
         ),
      },
      {
         element: '#integrationCategory-ads-account',
         intro: 'Integrate all your ad platforms under one roof.',
         position: 'top',
         available: integrations.some(
            (integration) =>
               integration.title === 'Ads Account' && integration.completed,
         ),
      },
      {
         element: '#integrationCategory-social-media',
         intro: 'Integrate all your social media platforms under one roof.',
         position: 'top',
         available: integrations.some(
            (integration) =>
               integration.title === 'Social Media' || integration.completed,
         ),
      },
      {
         element: '#integrationCategory-marketplace',
         intro: 'Integrate all your marketplace platforms under one roof.',
         position: 'top',
         available: integrations.some(
            (integration) =>
               integration.title === 'Marketplace' && integration.completed,
         ),
      },
   ];

   const startTour = () => {
      // Ensure only steps with `available` and matching DOM elements are included
      const availableSteps = steps.filter((step) => {
         const elementExists = document.querySelector(step.element) !== null; // Ensure element exists in the DOM
         return step.available && elementExists;
      });

      // Debugging: Log available steps and DOM status
      console.log('Available Steps:', availableSteps);

      if (availableSteps.length > 0) {
         intro.setOptions({ steps: availableSteps });
         void intro.start();

         dispatch(
            setFlag({ componentName: componentNames.INTEGRATION, flag: false }),
         );
      } else {
         console.log('No data available to start the tour.');
      }
   };

   useEffect(() => {
      if (integration) {
         startTour();
      }
   }, [integration]);
   return (
      <div className='SocialListening'>
         <Box>
            <Flex align='center'>
               <Heading as='h4' size='lg' className='Main' fontWeight='600'>
                  Integrations
                  <TooltipIcon
                     label={content.integration}
                     placement='top'
                     iconColor='blue.500'
                     ml={2}
                     mt={1}
                     boxSize={4}
                  />
               </Heading>
            </Flex>
         </Box>
         <div className='widget-container'>
            {integrations.map((integration) => (
               <Integration
                  id={`integrationCategory-${integration.title.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`}
                  key={integration.title
                     .replace(/[^a-zA-Z0-9]/g, '-')
                     .toLowerCase()}
                  components={integration.components}
                  title={integration.title}
                  componentKey={integration.componentKey}
                  completed={integration.completed}
               />
            ))}
         </div>
      </div>
   );
};

export default SocialListening;
